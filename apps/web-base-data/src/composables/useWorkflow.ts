import { ref } from 'vue';

import { getApprovalDetail<PERSON>pi, getProcessDefinition<PERSON>pi } from '#/api';

export async function useWorkflow(options?) {
  console.log(options);
  const processDefinition = ref<any>({});
  const processInstance = ref<any>({});
  const getProcessDefinitionDetail = async () => {
    try {
      processDefinition.value = await getProcessDefinitionApi({ key: options.key });
      console.log(processDefinition.value);
    } catch (error) {
      console.error('获取流程定义失败', error);
    }
  };
  const getApprovalDetail = async () => {
    processInstance.value = await getApprovalDetailApi({
      processInstanceId: options.processInstanceId,
    });
  };
  await getProcessDefinitionDetail();
  await getApprovalDetail();
  return {
    processDefinition: processDefinition.value,
    processInstance: processInstance.value,
  };
}
