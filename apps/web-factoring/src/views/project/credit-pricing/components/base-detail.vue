<script setup lang="ts">
import { BasicCaption } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';

defineProps({
  creditPricingForm: { type: Object, default: () => ({}) },
  descriptionsProp: { type: Object, default: () => ({}) },
});
const dictStore = useDictStore();
</script>

<template>
  <div>
    <a-descriptions v-bind="descriptionsProp" class="mt-4">
      <a-descriptions-item label="关联综合授信项目">
        {{ creditPricingForm.projectName }}
      </a-descriptions-item>
      <a-descriptions-item label="项目类型">
        {{ dictStore.formatter(creditPricingForm.projectType, 'FCT_PROJECT_TYPE') }}
      </a-descriptions-item>
      <a-descriptions-item label="项目定价名称">
        {{ creditPricingForm.pricingName }}
      </a-descriptions-item>
      <!--      <a-descriptions-item label="授信企业">-->
      <!--        {{ creditPricingForm.creditCompanyName }}-->
      <!--      </a-descriptions-item>-->
      <a-descriptions-item label="客户类别">
        {{ dictStore.formatter(creditPricingForm.customerCategory, 'FCT_CUSTOMER_CATEGORY') }}
      </a-descriptions-item>
      <a-descriptions-item label="客户分级">
        {{ dictStore.formatter(creditPricingForm.customerLevel, 'COMPANY_RATING') }}
      </a-descriptions-item>
      <a-descriptions-item label="行业属性">
        {{ creditPricingForm.industryAttributes }}
      </a-descriptions-item>
      <a-descriptions-item label="授信额度测算结果">
        {{ creditPricingForm.creditCalculateAmount }}
      </a-descriptions-item>
      <a-descriptions-item label="区域特性">
        {{ creditPricingForm.areaCharacteristics }}
      </a-descriptions-item>
      <a-descriptions-item label="底层项目">
        {{ creditPricingForm.bottomProject }}
      </a-descriptions-item>
      <a-descriptions-item label="担保方式">
        {{ creditPricingForm.guaranteeMethodDesc }}
      </a-descriptions-item>
      <a-descriptions-item label="其他情况" :span="2">
        {{ creditPricingForm.pricingOtherDesc }}
      </a-descriptions-item>
    </a-descriptions>
    <BasicCaption content="授信批复方案" />
    <a-descriptions v-bind="descriptionsProp" class="mt-4">
      <a-descriptions-item label="授信额度（元）">
        {{ creditPricingForm.pricingCreditAmount }}
      </a-descriptions-item>
      <a-descriptions-item label="授信期限（个月）">
        {{ creditPricingForm.pricingCreditTerm }}
      </a-descriptions-item>
      <a-descriptions-item label="授信费率（%）">
        {{ creditPricingForm.pricingCreditRate }}
      </a-descriptions-item>
      <a-descriptions-item label="授信额度类型">
        {{ dictStore.formatter(creditPricingForm.pricingCreditType, 'FCT_CREDIT_TYPE') }}
      </a-descriptions-item>
    </a-descriptions>
  </div>
</template>

<style scoped></style>
