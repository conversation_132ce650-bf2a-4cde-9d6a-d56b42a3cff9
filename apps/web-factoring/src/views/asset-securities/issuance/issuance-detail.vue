<script setup lang="ts">
import type { BasicAssetInfo } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';
import { formatDate } from '@vben/utils';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { getAssetIssueInfoApi } from '#/api';

defineEmits(['register']);
const dictStore = useDictStore();
// 详情页样式配置
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};

// 初始化数据
const init = async (data: BasicAssetInfo) => {
  // 获取详情数据，如果有ID则从接口获取，否则使用传入的数据
  const info = data.id ? await getAssetIssueInfoApi(data.id as number) : data;
  issuanceForm.value = { ...issuanceForm.value, ...info };
};

const issuanceForm = ref<BasicAssetInfo>({});
const [registerPopup] = usePopupInner(init);
</script>

<template>
  <BasicPopup title="ABS发行信息" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <!-- 基础信息 -->
        <a-descriptions-item label="关联ABS项目">
          {{ issuanceForm.absProjectName }}
        </a-descriptions-item>
        <a-descriptions-item label="发行登记编号">
          {{ issuanceForm.issueCode }}
        </a-descriptions-item>
        <a-descriptions-item label="ABS发行证券名称">
          {{ issuanceForm.securityName }}
        </a-descriptions-item>
        <a-descriptions-item label="ABS发行证券代码">
          {{ issuanceForm.securityCode }}
        </a-descriptions-item>

        <!-- 监管与交易信息 -->
        <a-descriptions-item label="监管机构">
          {{ issuanceForm.regulatoryAgency }}
        </a-descriptions-item>
        <a-descriptions-item label="交易场所">
          {{ issuanceForm.tradingVenue }}
        </a-descriptions-item>

        <!-- 发行信息 -->
        <a-descriptions-item label="发行起始日">
          {{ formatDate(issuanceForm.issueStartDate) }}
        </a-descriptions-item>
        <a-descriptions-item label="发行金额（元）">
          {{ issuanceForm.issueAmount }}
        </a-descriptions-item>
        <a-descriptions-item label="分层比例（%）">
          {{ issuanceForm.trancheRatio }}
        </a-descriptions-item>
        <a-descriptions-item label="信用支持（%）">
          {{ issuanceForm.creditSupport }}
        </a-descriptions-item>
        <a-descriptions-item label="发行利率（%）">
          {{ issuanceForm.issueRate }}
        </a-descriptions-item>
        <a-descriptions-item label="利率类型">
          {{ dictStore.formatter(issuanceForm.interestRateType, 'FCT_ABS_ISSUE_INTEREST_RATE') }}
        </a-descriptions-item>
        <a-descriptions-item label="还本方式">
          {{ dictStore.formatter(issuanceForm.repaymentMethod, 'FCT_ABS_ISSUE_REPAYMENT_METHOD') }}
        </a-descriptions-item>

        <!-- 其他信息 -->
        <a-descriptions-item label="当前余额（元）">
          {{ issuanceForm.currentBalance }}
        </a-descriptions-item>
        <a-descriptions-item label="特殊条款" :span="2">
          {{ issuanceForm.specialTerms }}
        </a-descriptions-item>
      </a-descriptions>
      <!-- 附件信息 -->
      <BaseAttachmentList :business-id="issuanceForm.id" business-type="FCT_ABS_ISSUE" />
      <BaseAttachmentList :business-id="issuanceForm.id" business-type="FCT_ABS_ISSUE_DISCLOSURE">
        <template #header>
          <BasicCaption content="信息批露材料" />
        </template>
      </BaseAttachmentList>
    </div>
  </BasicPopup>
</template>

<style></style>
