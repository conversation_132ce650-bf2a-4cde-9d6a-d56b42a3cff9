<script setup lang="ts">
import type { AssetIssueInfo } from '#/api';

import { reactive, ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';

import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { absProjectListApi, editAssetIssueApi, getAssetIssueInfoApi } from '#/api';

const emit = defineEmits(['ok', 'register']);

const dictStore = useDictStore();
const init = async (data: AssetIssueInfo) => {
  issuanceForm.value = {} as AssetIssueInfo;
  if (data.id) {
    const info = await getAssetIssueInfoApi(data.id);
    if (info.issueStartDate) info.issueStartDate = dayjs(info.issueStartDate).valueOf().toString();
    issuanceForm.value = info;
  }
};

const projectOptions = ref<any[]>([]);
const getProjectList = async () => {
  projectOptions.value = await absProjectListApi({ status: 'EFFECTIVE' });
};
getProjectList();

const [registerPopup, { closePopup }] = usePopupInner(init);
const FormRef = ref();
const issuanceForm = ref<AssetIssueInfo>({} as AssetIssueInfo);

const loading = reactive({
  submit: false,
});

const save = async () => {
  await FormRef.value.validate();
  const formData = cloneDeep(issuanceForm.value);
  formData.issueStartDate = Number(formData.issueStartDate);
  loading.submit = true;
  try {
    const res = await editAssetIssueApi(formData);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    loading.submit = false;
  }
};

const rules = {
  absProjectId: [{ required: true, message: '请选择关联ABS项目', trigger: 'change' }],
  securityCode: [{ required: true, message: '请输入ABS发行证券代码', trigger: 'blur' }],
  securityName: [{ required: true, message: '请输入ABS发行证券名称', trigger: 'blur' }],
  tradingVenue: [{ required: true, message: '请输入交易场所', trigger: 'blur' }],
  regulatoryAgency: [{ required: true, message: '请输入监管机构', trigger: 'blur' }],
  creditSupport: [{ required: true, message: '请输入信用支持', trigger: 'blur' }],
  issueStartDate: [{ required: true, message: '请选择发行起始日', trigger: 'change' }],
  issueAmount: [{ required: true, message: '请输入发行金额', trigger: 'blur' }],
  trancheRatio: [{ required: true, message: '请输入分层比例', trigger: 'blur' }],
  issueRate: [{ required: true, message: '请输入发行利率', trigger: 'blur' }],
  interestRateType: [{ required: true, message: '请选择利率类型', trigger: 'change' }],
  repaymentMethod: [{ required: true, message: '请选择还本方式', trigger: 'change' }],
  currentBalance: [{ required: true, message: '请输入当前余额', trigger: 'blur' }],
};

const formProp = { ...FORM_PROP, labelCol: { span: 6 }, wrapperCol: { span: 18 } };
const colSpan = COL_SPAN_PROP;
const fullProp = { ...FULL_FORM_ITEM_PROP, labelCol: { span: 3 }, wrapperCol: { span: 21 } };
</script>

<template>
  <BasicPopup v-bind="$attrs" title="发行登记" @register="registerPopup">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" :loading="loading.submit" @click="save">提交</a-button>
      </a-space>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-form ref="FormRef" class="mt-5" :model="issuanceForm" :rules="rules" v-bind="formProp">
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="关联ABS项目" name="absProjectId">
              <a-select
                v-model:value="issuanceForm.absProjectId"
                :options="projectOptions"
                :field-names="{ label: 'projectName', value: 'id' }"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="发行登记编号">
              {{ issuanceForm.issueCode }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="ABS发行证券名称" name="securityName">
              <a-input v-model:value="issuanceForm.securityName" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="ABS发行证券代码" name="securityCode">
              <a-input v-model:value="issuanceForm.securityCode" />
            </a-form-item>
          </a-col>
        </a-row>

        <BasicCaption content="监管与交易信息" />
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="监管机构" name="regulatoryAgency">
              <a-input v-model:value="issuanceForm.regulatoryAgency" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="交易场所" name="tradingVenue">
              <a-input v-model:value="issuanceForm.tradingVenue" />
            </a-form-item>
          </a-col>
        </a-row>

        <BasicCaption content="发行信息" />
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="发行起始日" name="issueStartDate">
              <a-date-picker v-model:value="issuanceForm.issueStartDate" value-format="x" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="发行金额（元）" name="issueAmount">
              <a-input v-model:value="issuanceForm.issueAmount" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="分层比例（%）" name="trancheRatio">
              <a-input v-model:value="issuanceForm.trancheRatio" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="信用支持（%）" name="creditSupport">
              <a-input v-model:value="issuanceForm.creditSupport" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="发行利率（%）" name="issueRate">
              <a-input v-model:value="issuanceForm.issueRate" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="利率类型" name="interestRateType">
              <a-select
                v-model:value="issuanceForm.interestRateType"
                :options="dictStore.getDictList('FCT_ABS_ISSUE_INTEREST_RATE')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="还本方式" name="repaymentMethod">
              <a-select
                v-model:value="issuanceForm.repaymentMethod"
                :options="dictStore.getDictList('FCT_ABS_ISSUE_REPAYMENT_METHOD')"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <BasicCaption content="其他信息" />
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="当前余额（元）" name="currentBalance">
              <a-input v-model:value="issuanceForm.currentBalance" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="特殊条款" name="specialTerms" v-bind="fullProp">
              <a-textarea v-model:value="issuanceForm.specialTerms" :rows="4" class="w-full" />
            </a-form-item>
          </a-col>
        </a-row>

        <BaseAttachmentList
          v-model="issuanceForm.attachmentList"
          :business-id="issuanceForm.id"
          business-type="FCT_ABS_ISSUE"
          edit-mode
        />
      </a-form>
    </div>
  </BasicPopup>
</template>

<style scoped></style>
