<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { BasicAssetInfo } from '#/api';

import { nextTick, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { DETAIL_GRID_OPTIONS } from '@vben/constants';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { cloneDeep, defineFormOptions, isEmpty } from '@vben/utils';

import { Modal as AntdModal, message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { assetPoolListApi, basicInputApi, batchBasicInputApi, delBasicAssetApi, getBasicAssetPageListApi } from '#/api';

import BasicAssetDetail from './basic-asset-detail.vue';
import BasicAssetEdit from './basic-asset-edit.vue';

const dictStore = useDictStore();

// 搜索表单配置（按原型字段调整）
const formOptions = defineFormOptions({
  schema: [
    { component: 'Input', fieldName: 'absAssetName', label: '基础资产名称' },
    { component: 'Input', fieldName: 'factoringContractCode', label: '合同号' },
    { component: 'Input', fieldName: 'creditorName', label: '债权人' },
    { component: 'Input', fieldName: 'debtorName', label: '债务人' },
    {
      component: 'Select',
      fieldName: 'assetClassification',
      label: '资产五级分类',
      componentProps: {
        options: dictStore.getDictList('FCT_ASSET_CLASSIFICATION'), // 关联资产五级分类字典
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '操作状态',
      componentProps: {
        options: dictStore.getDictList('FCT_STATUS'),
      },
    },
    {
      component: 'Select',
      fieldName: 'assetStatus',
      label: '基础资产状态',
      componentProps: {
        options: dictStore.getDictList('FCT_ABS_ASSET_STATUS'), // 关联基础资产状态字典
      },
    },
  ],
  commonConfig: { labelCol: { span: 6 }, wrapperCol: { span: 18 } },
});

// 表格列配置（按原型顺序 & 接口字段映射）
const gridOptions: VxeTableGridOptions = {
  checkboxConfig: {
    checkMethod: ({ row }) => {
      return ['EFFECTIVE'].includes(row.status) && ['WAIT_IN_POOL'].includes(row.assetStatus);
    },
  },
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'absAssetName', title: '基础资产名称', minWidth: 200 },
    { field: 'factoringContractCode', title: '合同号', minWidth: 180 },
    { field: 'receivableAmount', title: '应收账款金额（元）', minWidth: 150 },
    { field: 'receivableAgeDays', title: '应收账款账龄（天）', minWidth: 150 },
    {
      field: 'assetClassification',
      title: '资产五级分类',
      minWidth: 150,
    },
    { field: 'debtorName', title: '债务人', minWidth: 200 },
    { field: 'creditorName', title: '债权人', minWidth: 200 },
    { field: 'financingAmount', title: '融资金额（元）', minWidth: 150 },
    { field: 'financingRate', title: '融资利率（%）', minWidth: 120 },
    { field: 'remainingDays', title: '剩余期限（天）', minWidth: 120 },
    {
      field: 'status',
      title: '操作状态',
      cellRender: { name: 'CellStatus', props: { code: 'FCT_STATUS' } },
      minWidth: 120,
    },
    {
      field: 'assetStatus',
      title: '基础资产状态',
      cellRender: { name: 'CellStatus', props: { code: 'FCT_ABS_ASSET_STATUS' } },
      minWidth: 120,
    },
    { field: 'action', title: '操作', fixed: 'right', width: 140, slots: { default: 'action' } },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) =>
        await getBasicAssetPageListApi({ current: page.currentPage, size: page.pageSize, ...formValues }),
    },
  },
  toolbarConfig: { slots: { tools: 'toolbar-tools' } },
};

const [Grid, gridApi] = useVbenVxeGrid({ formOptions, gridOptions });
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();

// 新增
const add = () => openFormPopup(true, {});
// 编辑
const edit = (row: BasicAssetInfo) => openFormPopup(true, row);
const editSuccess = () => gridApi.reload();
// 查看详情
const viewDetail = (row: BasicAssetInfo) => openDetailPopup(true, row);
// 删除
const del = (row: BasicAssetInfo) => {
  AntdModal.confirm({
    title: '确认删除',
    content: '此操作将删除该基础资产，是否继续？',
    async onOk() {
      await delBasicAssetApi(row.id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};

// 入池弹窗（保留原有逻辑，若字段不符需调整）
const inputForm = ref({});
const poolOptions = ref<any[]>([]);

const getPoolList = async () => {
  poolOptions.value = await assetPoolListApi({ status: 'EFFECTIVE', packageStatus: 'un_packaged' });
};
getPoolList();
const rules = {
  poolId: [{ required: true, message: '请选择ABS资产池', trigger: 'change' }],
};
const UploadFormRef = ref();
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    await UploadFormRef.value.validate();
    const data = cloneDeep(inputForm.value);
    if (data.assetIdList.length === 1) {
      data.assetId = data.assetIdList[0].id;
    } else {
      data.assetIds = data.assetIdList.map((item) => item.id);
    }
    const api = data.assetIdList.length === 1 ? basicInputApi : batchBasicInputApi;
    await api(data);
    message.success($t('base.resSuccess'));
    await modalApi.close();
    await gridApi.reload();
  },
  onClosed: () => {
    inputForm.value = {};
  },
});
const inputGridOptions = {
  columns: [
    { field: 'absAssetName', title: '基础资产名称', minWidth: 200 },
    { field: 'factoringContractCode', title: '合同号', minWidth: 180 },
    { field: 'receivableAmount', title: '应收账款金额（元）', minWidth: 150 },
    { field: 'receivableAgeDays', title: '应收账款账龄（天）', minWidth: 150 },
    {
      field: 'assetClassification',
      title: '资产五级分类',
      minWidth: 150,
    },
    { field: 'debtorName', title: '债务人', minWidth: 200 },
    { field: 'creditorName', title: '债权人', minWidth: 200 },
    { field: 'financingAmount', title: '融资金额（元）', minWidth: 150 },
    { field: 'financingRate', title: '融资利率（%）', minWidth: 120 },
    { field: 'remainingDays', title: '剩余期限（天）', minWidth: 120 },
  ],
  ...DETAIL_GRID_OPTIONS,
} as VxeTableGridOptions;

const [InputGrid, inputGridApi] = useVbenVxeGrid({ gridOptions: inputGridOptions });
const input = (row: any) => {
  if (row === 'batch') {
    const assetIdList = gridApi.grid.getCheckboxRecords();
    if (isEmpty(assetIdList)) return message.warning('请选择要入池的基础资产');
  }
  inputForm.value.assetIdList = row === 'batch' ? gridApi.grid.getCheckboxRecords() : [row];
  modalApi.open();
  nextTick(() => {
    inputGridApi.grid.reloadData(inputForm.value.assetIdList);
  });
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button type="primary" @click="add">{{ $t('base.add') }}</a-button>
          <a-button type="primary" @click="input('batch')">批量入池</a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link v-if="['SUBMIT'].includes(row.status)" @click="edit(row)">
            {{ $t('base.edit') }}
          </a-typography-link>
          <a-typography-link @click="viewDetail(row)">{{ $t('base.detail') }}</a-typography-link>
          <a-typography-link type="danger" v-if="['SUBMIT'].includes(row.status)" @click="del(row)">
            {{ $t('base.del') }}
          </a-typography-link>
          <a-typography-link
            v-if="['EFFECTIVE'].includes(row.status) && ['WAIT_IN_POOL'].includes(row.assetStatus)"
            @click="input(row)"
          >
            入池
          </a-typography-link>
        </a-space>
      </template>
    </Grid>
    <BasicAssetEdit @register="registerForm" @ok="editSuccess" />
    <BasicAssetDetail @register="registerDetail" />
    <Modal title="入池" class="w-[1000px]">
      <a-form ref="UploadFormRef" :model="inputForm" :rules="rules">
        <a-form-item label="ABS资产池" name="poolId" class="w-[500px]">
          <a-select
            v-model:value="inputForm.poolId"
            :options="poolOptions"
            :field-names="{ label: 'assetPoolName', value: 'id' }"
          />
        </a-form-item>
        <InputGrid />
      </a-form>
    </Modal>
  </Page>
</template>
