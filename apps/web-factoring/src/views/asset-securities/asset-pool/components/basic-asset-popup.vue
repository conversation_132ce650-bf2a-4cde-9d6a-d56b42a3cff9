<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { useVbenModal } from '@vben/common-ui';
import { defineFormOptions } from '@vben/utils';

import { message } from 'ant-design-vue';
import { isEmpty } from 'lodash-es';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getBasicAssetPageListApi } from '#/api';

const formOptions = defineFormOptions({
  schema: [
    { component: 'Input', fieldName: 'absAssetName', label: '基础资产名称' },
    { component: 'Input', fieldName: 'factoringContractCode', label: '合同号' },
    { component: 'Input', fieldName: 'creditorName', label: '债权人' },
    { component: 'Input', fieldName: 'debtorName', label: '债务人' },
  ],
  showCollapseButton: false,
  submitOnEnter: true,
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'absAssetName', title: '基础资产名称', minWidth: 200 },
    { field: 'factoringContractCode', title: '合同号', minWidth: 180 },
    { field: 'receivableAmount', title: '应收账款金额（元）', minWidth: 150 },
    { field: 'receivableAgeDays', title: '应收账款账龄（天）', minWidth: 150 },
    {
      field: 'assetClassification',
      title: '资产五级分类',
      minWidth: 150,
    },
    { field: 'debtorName', title: '债务人', minWidth: 200 },
    { field: 'creditorName', title: '债权人', minWidth: 200 },
    { field: 'financingAmount', title: '融资金额（元）', minWidth: 150 },
    { field: 'financingRate', title: '融资利率（%）', minWidth: 120 },
    { field: 'remainingDays', title: '剩余期限（天）', minWidth: 120 },
  ],
  height: 500,
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getBasicAssetPageListApi({
          ...formValues,
          statusList: 'EFFECTIVE',
          assetStatusList: 'WAIT_IN_POOL',
          current: page.currentPage,
          size: page.pageSize,
        });
      },
    },
  },
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const [Modal, modalApi] = useVbenModal({
  onOpened: () => {},
  onConfirm: async () => {
    const selectedRows = gridApi.grid.getCheckboxRecords();
    if (isEmpty(selectedRows)) {
      return message.error('请选择数据');
    }
    modalApi.setData(selectedRows);
    await modalApi.close();
  },
});
</script>

<template>
  <Modal title="选择基础资产入池" class="w-[80vw]">
    <Grid />
  </Modal>
</template>

<style></style>
