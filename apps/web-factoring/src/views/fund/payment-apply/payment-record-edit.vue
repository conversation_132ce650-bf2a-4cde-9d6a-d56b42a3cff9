<script setup lang="ts">
import type { PaymentRecordInfo } from '#/api';

import { reactive, ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';

import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { cloneDeep, omit } from 'lodash-es';

import { BaseFilePickList } from '#/adapter/base-ui';
import {
  addPaymentRecordApi,
  getCompanyBankListApi,
  getCompanyDetailByCodeApi,
  getCreditApplyInfoApi,
  getPaymentApplyInfoApi,
  getPricingByProjectIdApi,
} from '#/api';
import LadderPenaltyDetail from '#/views/project/components/ladder-penalty-detail.vue';
import RepaymentCalculation from '#/views/project/components/repayment-calculation.vue';

const emit = defineEmits(['ok', 'register']);
const dictStore = useDictStore();
const init = async (data: PaymentRecordInfo) => {
  recordForm.value = {};
  if (data.id) {
    const info = data.id ? await getPaymentApplyInfoApi(data.id as number) : data;
    info.confirmInvestAmount = info.investAmount;
    recordForm.value = { ...recordForm.value, ...info };
    recordForm.value.payerCompanyCode = '91361200MAC5TQUL5M';
    const companyDetail = await getCompanyDetailByCodeApi({ code: recordForm.value.payerCompanyCode });
    recordForm.value.payerCompanyName = companyDetail.companyName;
    getCompanyBankList();
    const api = info.projectCreditApplyId ? getCreditApplyInfoApi : getPricingByProjectIdApi;
    const res = await api((info.projectCreditApplyId as number) || (info.projectId as number));
    delete res.id;
    const calculation = omit(res.calculation, 'id', 'projectName', 'projectCode');
    recordForm.value = { ...recordForm.value, ...res, ...calculation };
    recordForm.value.expectedLaunchDate = dayjs(recordForm.value.expectedLaunchDate).valueOf().toString();
    delete recordForm.value.expectedDueDate;
    RepaymentCalculationRef.value.init(recordForm.value);
  }
};
const payerBankOptions = ref<any[]>([]);
const getCompanyBankList = async () => {
  const res = await getCompanyBankListApi({ code: recordForm.value.payerCompanyCode });
  payerBankOptions.value = res;
};
const [registerPopup, { closePopup }] = usePopupInner(init);
const FormRef = ref();
const recordForm = ref<PaymentRecordInfo>({});

const loading = reactive({
  submit: false,
});
const RepaymentCalculationRef = ref();
const save = async () => {
  await FormRef.value.validate();
  await RepaymentCalculationRef.value.save();
  const formData = cloneDeep(recordForm.value);
  formData.expectInvestDate = Number(formData.expectInvestDate);
  formData.confirmInvestDate = Number(formData.confirmInvestDate);
  formData.expectedLaunchDate = Number(formData.expectedLaunchDate);
  formData.expectedDueDate = Number(formData.expectedDueDate);
  formData.calculation = { ...formData };
  formData.paymentApplyId = formData.id;
  delete formData.detailList;
  loading.submit = true;
  try {
    const res = await addPaymentRecordApi(formData as PaymentRecordInfo);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    loading.submit = false;
  }
};
const selectBankBranch = (_value: number, data: object) => {
  recordForm.value.payerBankBranch = data.accountName;
  recordForm.value.payerBankAccount = data.account;
};
const rules = {
  projectId: [{ required: true, message: '请选择关联项目', trigger: 'change' }],
  paymentMethod: [{ required: true, message: '请选择付款方式', trigger: 'change' }],
  expectInvestDate: [{ required: true, message: '请选择预计投放日期', trigger: 'change' }],
  confirmInvestDate: [{ required: true, message: '请选择确认投放日期', trigger: 'change' }],
  confirmPaymentMethod: [{ required: true, message: '请选择实际付款方式', trigger: 'change' }],
  fileId: [{ required: true, message: '请上传付款凭证' }],
  investAmount: [{ required: true, message: '请输入投放金额', trigger: 'blur' }],
  payeeCompanyCode: [{ required: true, message: '请选择收款单位', trigger: 'change' }],
  payeeBankAccount: [{ required: true, message: '请输入收款单位银行账号', trigger: 'blur' }],
  payeeBankId: [{ required: true, message: '请选择收款单位开户行名称', trigger: 'change' }],
  expectedDueDate: [{ required: true, message: '请选择最后还款日', trigger: 'change' }],
};
const formProp = { ...FORM_PROP, labelCol: { span: 6 }, wrapperCol: { span: 18 } };
const colSpan = COL_SPAN_PROP;
const fullProp = { ...FULL_FORM_ITEM_PROP, labelCol: { span: 3 }, wrapperCol: { span: 21 } };
const pickFile = async (data: any) => {
  recordForm.value.attachmentList = [data.id];
  recordForm.value.fileId = data.id;
  FormRef.value.clearValidate(['fileId']);
  recordForm.value.businessType = 'FCT_PAYMENT_CONFIRM_VOUCHER';
};
const confirmInvestDateChange = () => {
  recordForm.value.expectedLaunchDate = recordForm.value.confirmInvestDate;
};
</script>

<template>
  <BasicPopup v-bind="$attrs" title="付款记录" @register="registerPopup">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" :loading="loading.submit" @click="save()">提交</a-button>
      </a-space>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-form ref="FormRef" class="mt-5" :model="recordForm" :rules="rules" v-bind="formProp">
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="项目名称" name="projectName">
              {{ recordForm.projectName }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="收款单位">
              {{ recordForm.payeeCompanyName }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="付款单位">
              {{ recordForm.payerCompanyName }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="收款单位开户行">
              {{ recordForm.payeeBankBranch }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="付款单位开户行" name="payerBankId">
              <a-select
                v-model:value="recordForm.payerBankId"
                :options="payerBankOptions"
                :field-names="{ label: 'accountName', value: 'id' }"
                @change="selectBankBranch"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="收款单位开户行">
              {{ recordForm.payeeBankAccount }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="付款单位银行账号" name="payerBankAccount">
              <a-input v-model:value="recordForm.payerBankAccount" />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 确认付款信息 -->
        <BasicCaption content="确认付款信息" />
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="申请投放金额（元）" name="investAmount">
              <a-input v-model:value="recordForm.investAmount" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="确认投放金额（元）" name="confirmInvestAmount">
              <a-input v-model:value="recordForm.confirmInvestAmount" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="计划投放日期">
              {{ recordForm.expectInvestDate }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="确认投放日期" name="confirmInvestDate">
              <a-date-picker
                v-model:value="recordForm.confirmInvestDate"
                value-format="x"
                class="w-full"
                @change="confirmInvestDateChange"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="实际付款方式" name="confirmPaymentMethod">
              <a-radio-group
                v-model:value="recordForm.confirmPaymentMethod"
                :options="dictStore.getDictList('FCT_PAYMENT_METHOD')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="上传付款凭证" name="fileId">
              <BaseFilePickList v-if="recordForm.id" v-model="recordForm.fileId" @pick="pickFile" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="备注" name="remarks" v-bind="fullProp">
              <a-textarea v-model:value="recordForm.remarks" :rows="4" class="w-full" />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 回款测算 -->
        <BasicCaption content="回款测算" />
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="合同利率(%/年)" name="nominalInterestRate">
              <a-input v-model:value="recordForm.nominalInterestRate" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="定价综合收益率(%/年)" name="pricingXirrRate">
              <a-input v-model:value="recordForm.pricingXirrRate" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="服务费金额(元)" name="serviceFeeAmount">
              <a-input v-model:value="recordForm.serviceFeeAmount" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="宽限期天数(日)" name="gracePeriodDays">
              <a-input v-model:value="recordForm.gracePeriodDays" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="宽限期费率(%/年)" name="gracePeriodRate">
              <a-input v-model:value="recordForm.gracePeriodRate" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="逾期罚息设置">
              {{ dictStore.formatter(recordForm.penaltyType, 'FCT_PENALTY_TYPE') }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan" v-if="recordForm.penaltyType === 'fixed'">
            <a-form-item label="固定罚息利率（%）">
              {{ recordForm.penaltyInterestRate }}
            </a-form-item>
          </a-col>
        </a-row>
        <LadderPenaltyDetail v-show="recordForm.penaltyType === 'ladder'" :penalty-form="recordForm" />
        <RepaymentCalculation ref="RepaymentCalculationRef" v-model="recordForm" calculation-type="PaymentRecord" />
      </a-form>
    </div>
  </BasicPopup>
</template>

<style></style>
