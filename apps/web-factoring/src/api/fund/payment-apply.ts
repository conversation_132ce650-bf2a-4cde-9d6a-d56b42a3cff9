import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface PaymentApplyInfo {
  /**
   * 付款申请编号
   */
  applyCode?: string;
  /**
   * 收款单位银行账号
   */
  bankAccount?: string;
  /**
   * 收款单位开户行名称
   */
  bankBranch?: string;
  /**
   * 预计投放日期
   */
  expectInvestDate?: Date;
  /**
   * 主键
   */
  id?: number;
  /**
   * 投放金额（元）
   */
  investAmount?: number;
  /**
   * 是否审批
   */
  isReview?: number;
  /**
   * 放款前落实事项
   */
  itemList?: PaymentApplyItemBO[];
  /**
   * 收款单位Code
   */
  payeeCompanyCode?: string;
  /**
   * 收款单位名称
   */
  payeeCompanyName?: string;
  /**
   * 付款单位Code
   */
  payerCompanyCode?: string;
  /**
   * 付款单位名称
   */
  payerCompanyName?: string;
  /**
   * 付款方式
   */
  paymentMethod?: string;
  /**
   * 用信申请金额（元）
   */
  projectApplyAmount?: number;
  /**
   * 用信申请ID
   */
  projectCreditApplyId?: number;
  /**
   * 用信申请名称
   */
  projectCreditApplyName?: string;
  /**
   * 项目ID
   */
  projectId?: number;
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 备注
   */
  remarks?: string;
  /**
   * 审批状态
   */
  reviewStatus?: string;
  /**
   * 操作状态
   */
  status?: string;
  [property: string]: any;
}

/**
 * PaymentApplyItemBO，付款申请放款前落实事项
 */
export interface PaymentApplyItemBO {
  /**
   * 主键
   */
  id?: number;
  /**
   * 需落实事项
   */
  implementationMatters?: string;
  /**
   * 是否已落实
   */
  isImplemented?: number;
  /**
   * 材料要求
   */
  materialRequirements?: string;
  /**
   * 运营事务
   */
  operationsAffairs?: string;
  /**
   * 付款申请ID
   */
  paymentApplyId?: number;
  /**
   * 备注
   */
  remarks?: string;
  [property: string]: any;
}

// 获取分页列表
export async function getPaymentApplyPageListApi(params: PageListParams) {
  return requestClient.get<PaymentApplyInfo[]>('/factoring/payment/apply/page', { params });
}

// 添加
export async function addPaymentApplyApi(data: PaymentApplyInfo) {
  return requestClient.post<PaymentApplyInfo>('/factoring/payment/apply/add', data);
}

// 编辑
export async function editPaymentApplyApi(data: PaymentApplyInfo) {
  return requestClient.post<PaymentApplyInfo>('/factoring/payment/apply/edit', data);
}

// 获取详情
export async function getPaymentApplyInfoApi(id: number) {
  return requestClient.get(`/factoring/payment/apply/detail/${id}`);
}

// 删除
export async function delPaymentApplyApi(id: number) {
  return requestClient.post(`/factoring/payment/apply/delete/${id}`);
}

// 获取分页列表
export async function getCompanyBankListApi(params: PageListParams) {
  return requestClient.get<PaymentApplyInfo[]>('/base/company/bank/list', { params });
}

export async function getCompanyInvoiceListApi(params: PageListParams) {
  return requestClient.get<PaymentApplyInfo[]>('/base/company/invoice/list', { params });
}
