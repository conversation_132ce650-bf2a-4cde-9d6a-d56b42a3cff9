import { requestClient } from '#/api/request';

export interface EarlyWarningRuleInfo {
  createTime?: number;
  updateBy?: string;
  updateTime?: number;
  id?: number;
  ruleName: string;
  ruleLevel: string;
  ruleDescription: string;
  triggerCondition: string;
  triggerConditionValue: string;
  enable: number;
  deleteFlag: number;
  version: number;
}
export async function getEarlyWarningRuleListApi() {
  return requestClient.get<EarlyWarningRuleInfo[]>('/early-warning/rule/list');
}
export async function addEarlyWarningRuleApi(data: EarlyWarningRuleInfo) {
  return requestClient.post('/early-warning/rule/add', data);
}
export async function editEarlyWarningRuleApi(data: EarlyWarningRuleInfo) {
  return requestClient.post('/early-warning/rule/edit', data);
}
