<script setup lang="ts">
import { computed, defineProps } from 'vue';

const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
});
const conditionParts = computed(() => {
  if (!props.item.triggerCondition || !props.item.triggerConditionValue) {
    return [];
  }
  return props.item.triggerCondition.split(props.item.triggerConditionValue);
});
</script>

<template>
  <p class="mt-2">
    <span>预警触发参数：</span>
    <span>{{ conditionParts[0] }}</span>
    <span class="text-lg text-red-500">{{ item.triggerConditionValue }}</span>
    <span>{{ conditionParts[1] }}</span>
  </p>
</template>

<style scoped></style>
