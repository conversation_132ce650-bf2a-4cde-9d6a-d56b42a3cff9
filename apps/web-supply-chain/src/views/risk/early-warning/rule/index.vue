<script setup lang="ts">
import type { EarlyWarningRuleInfo } from '#/api';

import { ref } from 'vue';

import { StatusTag } from '@vben/base-ui';
import { Page, useVbenModal } from '@vben/common-ui';

import { SettingOutlined } from '@ant-design/icons-vue';
import { Flex } from 'ant-design-vue';

import { getEarlyWarningRuleListApi } from '#/api';
import RuleForm from '#/views/risk/early-warning/rule/components/rule-form.vue';
import TriggerConditionItem from '#/views/risk/early-warning/rule/components/trigger-condition-item.vue';

const ruleList = ref<(EarlyWarningRuleInfo & { loading?: boolean })[]>([]);
const getRuleList = async () => {
  ruleList.value = await getEarlyWarningRuleListApi();
};
getRuleList();
const changeStatus = async (checked: number, item: EarlyWarningRuleInfo & { loading?: boolean }) => {
  item.loading = true;
  await new Promise((resolve) => setTimeout(resolve, 1000));
  item.loading = false;
};
const [Model, modalApi] = useVbenModal({
  connectedComponent: RuleForm,
  onOpenChange: (isOpen) => {
    if (!isOpen) {
      getRuleList();
    }
  },
});
const edit = (item: EarlyWarningRuleInfo) => {
  modalApi.setData(item).open();
};
</script>

<template>
  <Page auto-content-height>
    <Flex wrap="wrap" gap="middle">
      <a-card v-for="item in ruleList" :title="item.ruleName" :key="item.id" class="w-[400px]">
        <template #extra>
          <!--<a-switch-->
          <!--  v-model:checked="item.enable"-->
          <!--  :checked-value="1"-->
          <!--  :un-checked-value="0"-->
          <!--  :loading="item.loading"-->
          <!--  @change="(checked: number) => changeStatus(checked, item)"-->
          <!--/>-->
        </template>
        <div class="flex min-h-[60px] flex-col justify-between">
          <div>
            <StatusTag code="earlyWarningLevel" :value="item.ruleLevel" />
          </div>
          <TriggerConditionItem :item="item" />
        </div>
        <template #actions>
          <a-switch
            v-model:checked="item.enable"
            :checked-value="1"
            :un-checked-value="0"
            :loading="item.loading"
            @change="(checked: number) => changeStatus(checked, item)"
          />
          <SettingOutlined style="font-size: 22px" @click="edit(item)" />
        </template>
      </a-card>
    </Flex>
    <Model />
  </Page>
</template>

<style></style>
