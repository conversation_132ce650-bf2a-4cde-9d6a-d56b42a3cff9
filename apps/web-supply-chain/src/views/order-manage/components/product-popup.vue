<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { useVbenModal } from '@vben/common-ui';
import { defineFormOptions } from '@vben/utils';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getProductListApi, getTaxonomyTreeListApi } from '#/api';

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'ApiTreeSelect',
      fieldName: 'categoryId',
      label: '商品分类',
      componentProps: {
        api: getTaxonomyTreeListApi,
        labelField: 'categoryName',
        valueField: 'id',
        childrenField: 'children',
      },
    },
    {
      component: 'Input',
      fieldName: 'productCode',
      label: '商品编码',
    },
    {
      component: 'Input',
      fieldName: 'productName',
      label: '商品名称',
    },
    {
      component: 'Input',
      fieldName: 'specifications',
      label: '规格型号',
    },
    {
      component: 'Input',
      fieldName: 'productAlias',
      label: '商品别名',
    },
  ],
  // 按下回车时是否提交表单
  submitOnEnter: true,
  commonConfig: {
    colon: false,
  },
  wrapperClass: 'grid-cols-1 md:grid-cols-3',
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'categoryName', title: '商品分类' },
    { field: 'productName', title: '商品名称' },
    { field: 'productCode', title: '商品编码' },
    { field: 'productAlias', title: '商品别名' },
    { field: 'specifications', title: '规格型号' },
    { field: 'measureUnit', title: '计量单位' },
    { field: 'brandName', title: '牌号/材质' },
    { field: 'originName', title: '产地/厂商' },
  ],
  height: 500,
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues: { spuCode: string; spuName: string; status: string }) => {
        return await getProductListApi({
          ...formValues,
          current: page.currentPage,
          size: page.pageSize,
        });
      },
    },
  },
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const [Modal, modalApi] = useVbenModal({
  onOpened: () => {},
  onConfirm: async () => {
    // 获取表格中选中的商品
    const selectedRows = gridApi.grid.getCheckboxRecords();
    // 去掉每个对象中的 id 字段
    const processedRows = selectedRows.map(({ id: _id, ...rest }) => rest);
    modalApi.setData(processedRows);
    modalApi.close();
  },
});
</script>

<template>
  <Modal title="商品选择" class="w-[80vw]">
    <Grid />
  </Modal>
</template>

<style></style>
