<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { Page } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { defineFormOptions, cloneDeep } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';
import { BaseAttachmentList } from '#/adapter/base-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { Rule } from 'ant-design-vue/es/form';
import {
  type EvaluateBaseInfo,
  evaluateAddApi,
  evaluateEditApi,
  evaluatePageApi,
  evaluateDeleteApi,
  downloadZipApi,
  projectManageListApi,
} from '#/api';
import {
  Col,
  DatePicker,
  Form,
  FormItem,
  Input,
  Modal,
  Row,
  Select,
  Modal as AntdModal,
  Button,
  Space,
  TypographyLink,
  message,
} from 'ant-design-vue';

interface SelectOption {
  projectName: string | undefined;
  id: number | string | undefined;
}
const projectInfoOptions = ref<SelectOption[]>([]);

const sortKey = ref<string>('create_time');
const visible = ref(false);
const formRef = ref();
const defaultForm: Partial<EvaluateBaseInfo> = {
  reportCode: undefined,
  reportName: undefined,
  projectId: undefined,
  projectName: undefined,
  settlementDate: undefined,
  status: undefined,
  approvalStatus: undefined,
  remark: undefined,
  attachmentList: [],
};
let detailForm = reactive<Partial<EvaluateBaseInfo>>(cloneDeep(defaultForm));

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'reportName',
      label: '项目后评价名称',
    },
    {
      component: 'RangePicker',
      fieldName: 'settlementDate',
      label: '评价时段',
    },
  ],
  fieldMappingTime: [
    // 时间范围映射
    ['receiptDate', ['settlementStartDate', 'settlementEndDate'], 'YYYY-MM-DD'],
  ],
  showCollapseButton: true,
  submitOnEnter: true,
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'seq', width: 60, fixed: 'left' }, // 添加序号列
    { field: 'reportName', title: '项目后评价名称' },
    { field: 'projectName', title: '项目名称' },
    { field: 'settlementDate', title: '评价时段', formatter: 'formatDate' },
    { field: 'createName', title: '创建人' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 200,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await evaluatePageApi({
          current: page.currentPage,
          size: page.pageSize,
          descs: sortKey.value,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const colSpan = { md: 24, sm: 24 };
const labelCol = { style: { width: '150px' } };
const rules: Record<string, Rule[]> = {
  projectId: [{ required: true, message: '关联的项目名称', trigger: 'change' }],
  settlementDate: [{ required: true, message: ' 结清日期', trigger: 'change' }],
  reportName: [{ required: true, message: '项目后评价名称', trigger: 'change' }],
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 创建
const add = () => {
  const $grid = gridApi.grid;
  if ($grid) {
    visible.value = true;
  }
};

const modalConfirm = async () => {
  // 校验表单
  await formRef.value.validate();
  if (!detailForm.attachmentList || detailForm.attachmentList.length === 0) {
    message.error('请上传项目后评价附件');
    return;
  }
  const api = detailForm.id ? evaluateEditApi : evaluateAddApi;
  detailForm.projectName = projectInfoOptions.value.find((p) => p.id == detailForm.projectId)?.projectName;
  const res = await api(detailForm as EvaluateBaseInfo);
  if (res) {
    await gridApi.reload();
    message.success($t('base.resSuccess'));
    visible.value = false;
  }
  visible.value = false;
  Object.assign(detailForm, cloneDeep(defaultForm));
};

// 重新上传
const upload = (row: EvaluateBaseInfo) => {
  if (row.id) {
    Object.assign(detailForm, cloneDeep(defaultForm));
    Object.assign(detailForm, JSON.parse(JSON.stringify(row)));
    visible.value = true;
  }
};

// 下载
const download = async (row: EvaluateBaseInfo) => {
  if (row.id && row.attachmentList && row.attachmentList.length > 0) {
    const ids = row.attachmentList.join(',');
    const res = await downloadZipApi({ ids });
    if (res) {
      window.open(res);
    } else {
      message.error('下载链接获取失败');
    }
  } else {
    message.warning('没有附件可下载');
  }
};

// 删除
const del = (row: EvaluateBaseInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmDelete'),
    content: $t('base.deleteConfirmContent'),
    okText: $t('base.confirm'),
    cancelText: $t('base.cancel'),
    onOk: async () => {
      // 这里可以调用删除API
      if (row.id) {
        await evaluateDeleteApi(row.id);
        // 删除成功后刷新列表
        await gridApi.reload();
      }
    },
  });
};

const modalCancel = () => {
  Object.assign(detailForm, cloneDeep(defaultForm));
  visible.value = false;
};

onMounted(async () => {
  const res = await projectManageListApi({ projectName: '' });
  projectInfoOptions.value = res.map((item) => ({
    projectName: item.projectName,
    id: item.id,
  }));
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="add">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.add') }}
        </Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="upload(row)"> 重新上传 </TypographyLink>
          <TypographyLink @click="download(row)"> 批量下载 </TypographyLink>
          <TypographyLink type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Modal v-model:open="visible" title="上传项目后评价" style="width: 800px" @ok="modalConfirm" @cancel="modalCancel">
      <div>
        <Form
          ref="formRef"
          :colon="false"
          :model="detailForm"
          :rules="rules"
          :label-col="labelCol"
          :wrapper-col="{ span: 20 }"
          class="px-8"
        >
          <Row class="mt-5">
            <Col v-bind="colSpan">
              <FormItem label="项目后评价名称" name="reportName">
                <Input v-model:value="detailForm.reportName" :disabled="!!detailForm.id" />
              </FormItem>
            </Col>

            <Col v-bind="colSpan">
              <FormItem label="关联项目" name="projectId">
                <Select
                  v-model:value="detailForm.projectId"
                  :options="projectInfoOptions"
                  show-search
                  :field-names="{ label: 'projectName', value: 'id' }"
                  :filter-option="(input: string, option: any) => option.projectName.includes(input)"
                  :disabled="!!detailForm.id"
                />
              </FormItem>
            </Col>

            <Col v-bind="colSpan">
              <FormItem label="结清日期" name="settlementDate">
                <DatePicker
                  v-model:value="detailForm.settlementDate"
                  value-format="YYYY-MM-DD hh:mm:ss"
                  format="YYYY-MM-DD"
                  :disabled="!!detailForm.id"
                />
              </FormItem>
            </Col>

            <Col v-bind="colSpan">
              <FormItem label="上传项目后评价" name="attachmentList">
                <BaseAttachmentList
                  v-model="detailForm.attachmentList"
                  :business-id="detailForm.id"
                  business-type="SCM_POST_PROJECT_EVALUATION"
                  edit-mode
                >
                  <template #header>
                    <BasicCaption content="" />
                  </template>
                </BaseAttachmentList>
              </FormItem>
            </Col>
          </Row>
        </Form>
      </div>
    </Modal>
  </Page>
</template>
<style scoped>
.ant-modal-body :deep(.ant-picker) {
  width: 100%;
}

:deep(.fe-basic-caption-border) {
  display: none;
}
</style>
