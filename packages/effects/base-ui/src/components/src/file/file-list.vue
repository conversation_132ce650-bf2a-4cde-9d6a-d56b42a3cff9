<script setup lang="ts">
import { ref, watch } from 'vue';

import { DeleteOutlined, DownloadOutlined, EyeOutlined } from '@ant-design/icons-vue';
import { TypographyLink } from 'ant-design-vue';

import { FilePreviewDialog } from '#/components';

interface FileInfo {
  id: number;
  originalName: string;
  link: string;
}

const props = defineProps({
  editMode: { type: Boolean, default: false },
  listType: { type: String, default: 'text' },
  fileInfoApi: { type: Function, required: true },
  previewExternalApi: { type: Function, default: null },
  downloadApi: { type: Function, default: null },
});
const FilePreviewDialogRef = ref();
const fileIds = defineModel<number | number[]>({ type: [Number, Array] });
const fileList = ref<FileInfo[]>([]);

watch(
  () => fileIds.value,
  async (ids) => {
    if (ids) {
      // 统一处理为数组格式
      const idsArray = Array.isArray(ids) ? ids : [ids];
      if (idsArray.length > 0) {
        fileList.value = await props.fileInfoApi(idsArray);
      }
    } else {
      fileList.value = [];
    }
  },
  { immediate: true },
);
const preview = (item: FileInfo) => {
  FilePreviewDialogRef.value.init(item.id);
};
const deleteFile = (index: number) => {
  fileList.value.splice(index, 1);

  // 更新 fileIds 模型值
  const remainingIds = fileList.value.map((item) => item.id);

  if (Array.isArray(fileIds.value)) {
    // 如果原来是数组，保持数组格式
    fileIds.value = remainingIds;
  } else {
    // 如果原来是单个数字，根据剩余文件数量决定返回格式
    if (remainingIds.length === 1) {
      fileIds.value = remainingIds[0];
    } else if (remainingIds.length === 0) {
      fileIds.value = undefined;
    } else {
      fileIds.value = remainingIds;
    }
  }
};
const download = async (item: FileInfo) => {
  const res = await props.downloadApi({ id: item.id });
  window.open(res, '_blank');
};
</script>

<template>
  <div>
    <template v-if="listType === 'text'">
      <TypographyLink v-for="(item, index) in fileList" :key="item.id" class="item-center flex">
        <span @click="preview(item)">{{ item.originalName }}</span>
        <DeleteOutlined v-if="editMode" class="ml-1" @click="deleteFile(index)" />
        <DownloadOutlined class="ml-1" @click="download(item)" />
      </TypographyLink>
    </template>
    <div v-else-if="listType === 'picture-card'" class="flex flex-wrap gap-2">
      <div v-for="(item, index) in fileList" :key="item.id" class="group relative h-24 w-24">
        <img
          :src="item.link"
          :alt="item.originalName"
          class="h-full w-full cursor-pointer rounded border object-cover"
          @click="!editMode ? preview(item) : undefined"
        />
        <!-- 悬浮操作层 -->
        <div
          v-if="editMode"
          class="absolute inset-0 flex items-center justify-center gap-3 rounded bg-black bg-opacity-50 opacity-0 transition-opacity duration-200 group-hover:opacity-100"
        >
          <EyeOutlined
            class="cursor-pointer text-lg text-white transition-colors hover:text-blue-400"
            @click.stop="preview(item)"
          />
          <DownloadOutlined
            class="cursor-pointer text-lg text-white transition-colors hover:text-blue-400"
            @click.stop="download(item)"
          />
          <DeleteOutlined
            class="cursor-pointer text-lg text-white transition-colors hover:text-red-400"
            @click.stop="deleteFile(index)"
          />
        </div>
      </div>
    </div>
    <FilePreviewDialog ref="FilePreviewDialogRef" :preview-api="previewExternalApi" :download-api="downloadApi" />
  </div>
</template>

<style scoped></style>
